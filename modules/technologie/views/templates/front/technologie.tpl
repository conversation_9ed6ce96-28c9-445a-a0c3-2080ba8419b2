{**
 * Front Office šablona pro zobrazení technologií potisku
 * Kompatibilní s PrestaShop 8.2.0 a moderními tématy
 *}

{extends file='page.tpl'}

{block name='page_title'}
    <h1 class="page-title">{$page_title|escape:'html':'UTF-8'}</h1>
{/block}

{block name='page_content'}
<div class="technologie-page">



    {* Úvodní sekce *}
    <div class="technologie-intro">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h2 class="section-title">{l s='Naše technologie potisku' mod='technologie'}</h2>
                    <p class="section-description">
                        {l s='Nabízíme širokou škálu moderních technologií potisku pro všechny typy materiálů a požadavků. Každá technologie má své specifické výhody a oblasti použití.' mod='technologie'}
                    </p>
                </div>
            </div>
        </div>
    </div>

    {* Debug informace pro celý seznam (pokud je debug mode) *}
    {if $debug_mode}
        <div class="container">
            <div class="alert alert-info" style="margin-bottom: 20px; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;">
                <h4 style="color: #0c5460;">🔧 Debug Mode - Seznam technologií</h4>
                <p style="margin: 5px 0;">Celkem technologií: {count($technologie)}</p>
                <p style="margin: 5px 0;">Debug parametr aktivní - zobrazují se dodatečné informace o URL.</p>
                <p style="margin: 5px 0;"><strong>Tip:</strong> Odeberte <code>?debug=1</code> z URL pro normální zobrazení.</p>
            </div>
        </div>
    {/if}

    {* Seznam technologií *}
    {if $technologie && count($technologie) > 0}
    <div class="technologie-grid">
        <div class="container">
            <div class="row g-4">
                {foreach from=$technologie item=tech}
                <div class="col-xl-4 col-lg-4 col-md-6">
                    <article class="technologie-card"
                             role="article"
                             aria-labelledby="tech-title-{$tech->getId()}"
                             tabindex="0">

                        {* Obrázek technologie *}
                        <div class="technologie-image">
                            {if $tech->getImage()}
                                <img src="{$tech->getImageUrl()}"
                                     alt="{$tech->getName()|escape:'html':'UTF-8'}"
                                     class="technologie-img"
                                     loading="lazy"
                                     decoding="async" />
                            {else}
                                <div class="technologie-placeholder">
                                    <i class="fas fa-print" aria-hidden="true"></i>
                                    <span>{l s='Bez obrázku' mod='technologie'}</span>
                                </div>
                            {/if}


                        </div>

                        {* Obsah karty *}
                        <div class="technologie-content">
                            <h3 class="technologie-title" id="tech-title-{$tech->getId()}">
                                {$tech->getName()|escape:'html':'UTF-8'}
                            </h3>

                            {if $tech->getDescription()}
                                <p class="technologie-description">
                                    {$tech->getDescription()|escape:'html':'UTF-8'|nl2br}
                                </p>
                            {/if}

                            {* Preview výhod technologie *}
                            {if $tech->getAdvantagesPreview() && count($tech->getAdvantagesPreview()) > 0}
                                <div class="technologie-advantages-preview">
                                    <h4 class="advantages-title">{l s='Hlavní výhody:' mod='technologie'}</h4>
                                    <ul class="advantages-list">
                                        {foreach from=$tech->getAdvantagesPreview() item=advantage}
                                            <li class="advantage-item">
                                                <i class="fas fa-check-circle" aria-hidden="true"></i>
                                                <span>{$advantage|escape:'html':'UTF-8'}</span>
                                            </li>
                                        {/foreach}
                                    </ul>
                                </div>
                            {/if}

                            {* Odkaz na detail technologie *}
                            <div class="technologie-actions">
                                {if $tech->detail_url}
                                    <a href="{$tech->detail_url}"
                                       class="technologie-detail-link"
                                       aria-label="{l s='Zobrazit detail technologie' mod='technologie'} {$tech->getName()|escape:'html':'UTF-8'}">
                                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                                        <span>{l s='Detail technologie' mod='technologie'}</span>
                                    </a>
                                {else}
                                    <button type="button"
                                            class="technologie-detail-btn disabled"
                                            disabled
                                            aria-label="{l s='Detail není dostupný' mod='technologie'}">
                                        <i class="fas fa-info-circle" aria-hidden="true"></i>
                                        <span>{l s='Více informací' mod='technologie'}</span>
                                    </button>
                                {/if}

                                {* Debug informace (pouze pokud je debug parametr) *}
                                {if isset($smarty.get.debug) && $smarty.get.debug == '1'}
                                    <div class="debug-url-info" style="margin-top: 10px; padding: 8px; background: #f8f9fa; font-size: 11px; border-radius: 3px; border: 1px solid #dee2e6;">
                                        <strong style="color: #495057;">🔍 Debug URL pro {$tech->getName()}:</strong><br>
                                        <div style="margin: 5px 0;">
                                            <strong>Slug:</strong> <code style="background: #e9ecef; padding: 2px 4px; border-radius: 2px;">{$tech->slug|default:'❌ N/A'}</code>
                                        </div>
                                        <div style="margin: 5px 0;">
                                            <strong>Final URL:</strong> <code style="background: #e9ecef; padding: 2px 4px; border-radius: 2px;">{$tech->detail_url|default:'❌ N/A'}</code>
                                        </div>
                                        {if isset($tech->debug_info)}
                                            <div style="margin: 5px 0;">
                                                <strong>Custom URL:</strong> <code style="background: #e9ecef; padding: 2px 4px; border-radius: 2px;">{$tech->debug_info.custom_url|default:'❌ N/A'}</code>
                                            </div>
                                            <div style="margin: 5px 0;">
                                                <strong>Fallback URL:</strong> <code style="background: #e9ecef; padding: 2px 4px; border-radius: 2px;">{$tech->debug_info.fallback_url|default:'❌ N/A'}</code>
                                            </div>
                                            <div style="margin: 5px 0;">
                                                <strong>Method:</strong> <span style="color: {if $tech->debug_info.custom_url_used}#28a745{else}#dc3545{/if};">{$tech->debug_info.generation_method|default:'unknown'}</span>
                                            </div>
                                        {/if}
                                        {if $tech->detail_url}
                                            <div style="margin-top: 8px;">
                                                <a href="{$tech->detail_url}" style="color: #007bff; text-decoration: none; font-size: 10px;" target="_blank">🔗 Test odkaz</a>
                                            </div>
                                        {/if}
                                    </div>
                                {/if}
                            </div>
                        </div>
                    </article>
                </div>
                {/foreach}
            </div>
        </div>
    </div>

    {* Kontaktní sekce *}
    <div class="technologie-contact">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="contact-title">{l s='Potřebujete poradit s výběrem technologie?' mod='technologie'}</h3>
                    <p class="contact-description">
                        {l s='Naši odborníci vám pomohou vybrat nejvhodnější technologii potisku pro váš projekt.' mod='technologie'}
                    </p>
                    <div class="contact-buttons">
                        <a href="{$urls.pages.contact}" class="btn btn-primary btn-lg">
                            <i class="fas fa-envelope"></i>
                            {l s='Kontaktujte nás' mod='technologie'}
                        </a>
                        {if isset($urls.pages.stores)}
                        <a href="{$urls.pages.stores}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-map-marker-alt"></i>
                            {l s='Naše pobočky' mod='technologie'}
                        </a>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {else}
    {* Prázdný stav *}
    <div class="technologie-empty">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 mx-auto text-center">
                    <div class="empty-state">
                        <i class="fas fa-print empty-icon"></i>
                        <h3 class="empty-title">{l s='Žádné technologie' mod='technologie'}</h3>
                        <p class="empty-description">
                            {l s='Momentálně nemáme k dispozici žádné technologie potisku. Zkuste to prosím později.' mod='technologie'}
                        </p>
                        <a href="{$urls.pages.index}" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            {l s='Zpět na hlavní stránku' mod='technologie'}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {/if}
</div>


{/block}

{block name='page_footer'}
    {* Structured data pro SEO *}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": "{$page_title|escape:'html':'UTF-8'}",
        "description": "{$page_description|escape:'html':'UTF-8'}",
        "numberOfItems": {if $technologie}{count($technologie)}{else}0{/if},
        "itemListElement": [
            {if $technologie}
            {foreach from=$technologie item=tech}
            {
                "@type": "ListItem",
                "position": {$tech@iteration},
                "name": "{$tech->getName()|escape:'html':'UTF-8'}",
                "description": "{if $tech->getDescription()}{$tech->getDescription()|escape:'html':'UTF-8'}{/if}"
            }{if !$tech@last},{/if}
            {/foreach}
            {/if}
        ]
    }
    </script>
{/block}
